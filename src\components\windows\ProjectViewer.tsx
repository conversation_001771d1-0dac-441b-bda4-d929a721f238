import React, { useState, useEffect } from 'react';
import { ArrowLeft, Home, Refresh, Globe } from 'lucide-react';

interface ProjectViewerProps {
  projectId: string;
  onNavigateBack?: () => void;
}

const ProjectViewer: React.FC<ProjectViewerProps> = ({ projectId, onNavigateBack }) => {
  const [htmlContent, setHtmlContent] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadProjectContent = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch(`/projects/${projectId}.html`);
        if (!response.ok) {
          throw new Error(`Failed to load project: ${response.statusText}`);
        }
        
        const content = await response.text();
        setHtmlContent(content);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load project');
      } finally {
        setLoading(false);
      }
    };

    loadProjectContent();
  }, [projectId]);

  if (loading) {
    return (
      <div className="h-full bg-white flex items-center justify-center" style={{ fontFamily: 'Tahoma, sans-serif' }}>
        <div className="text-center bg-gray-100 border-2 border-gray-400 p-8 rounded" style={{
          borderTopColor: '#ffffff',
          borderLeftColor: '#ffffff',
          borderRightColor: '#808080',
          borderBottomColor: '#808080'
        }}>
          <div className="text-blue-600 text-4xl mb-4">🌐</div>
          <p className="text-gray-700 text-sm mb-2">Internet Explorer</p>
          <p className="text-gray-600 text-xs">Loading project content...</p>
          <div className="mt-4 bg-white border border-gray-400 h-2 w-48 mx-auto overflow-hidden">
            <div className="h-full bg-blue-500 animate-pulse"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full bg-white flex items-center justify-center" style={{ fontFamily: 'Tahoma, sans-serif' }}>
        <div className="text-center bg-gray-100 border-2 border-gray-400 p-8 rounded max-w-md" style={{
          borderTopColor: '#ffffff',
          borderLeftColor: '#ffffff',
          borderRightColor: '#808080',
          borderBottomColor: '#808080'
        }}>
          <div className="text-red-600 text-4xl mb-4">❌</div>
          <h3 className="text-sm font-bold text-gray-800 mb-2">Internet Explorer</h3>
          <p className="text-xs text-gray-600 mb-4">The page cannot be displayed</p>
          <p className="text-xs text-gray-500 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-3 py-1 text-xs bg-gradient-to-b from-gray-100 to-gray-200 border border-gray-400 hover:from-gray-200 hover:to-gray-300 active:from-gray-300 active:to-gray-200 transition-all"
            style={{
              borderTopColor: '#ffffff',
              borderLeftColor: '#ffffff',
              borderRightColor: '#808080',
              borderBottomColor: '#808080'
            }}
          >
            Refresh
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-white flex flex-col">
      {/* Windows XP Style Browser Bar */}
      <div className="bg-gradient-to-b from-blue-50 to-blue-100 border-b-2 border-gray-400 px-3 py-2" style={{ fontFamily: 'Tahoma, sans-serif' }}>
        <div className="flex items-center gap-2">
          {/* Navigation Buttons */}
          <div className="flex items-center gap-1">
            {onNavigateBack && (
              <button
                onClick={onNavigateBack}
                className="px-2 py-1 text-xs bg-gradient-to-b from-gray-100 to-gray-200 border border-gray-400 hover:from-gray-200 hover:to-gray-300 active:from-gray-300 active:to-gray-200 transition-all"
                style={{
                  borderTopColor: '#ffffff',
                  borderLeftColor: '#ffffff',
                  borderRightColor: '#808080',
                  borderBottomColor: '#808080'
                }}
                title="Back to Projects"
              >
                ← Back
              </button>
            )}
            <button
              onClick={() => window.location.reload()}
              className="px-2 py-1 text-xs bg-gradient-to-b from-gray-100 to-gray-200 border border-gray-400 hover:from-gray-200 hover:to-gray-300 active:from-gray-300 active:to-gray-200 transition-all"
              style={{
                borderTopColor: '#ffffff',
                borderLeftColor: '#ffffff',
                borderRightColor: '#808080',
                borderBottomColor: '#808080'
              }}
              title="Refresh"
            >
              🔄
            </button>
            <button
              className="px-2 py-1 text-xs bg-gradient-to-b from-gray-100 to-gray-200 border border-gray-400 hover:from-gray-200 hover:to-gray-300 active:from-gray-300 active:to-gray-200 transition-all"
              style={{
                borderTopColor: '#ffffff',
                borderLeftColor: '#ffffff',
                borderRightColor: '#808080',
                borderBottomColor: '#808080'
              }}
              title="Home"
            >
              🏠
            </button>
          </div>

          {/* Address Bar */}
          <div className="flex-1 flex items-center gap-2">
            <span className="text-xs text-gray-700">Address</span>
            <div className="flex-1 bg-white border-2 border-gray-400 px-2 py-1 text-xs font-mono" style={{
              borderTopColor: '#808080',
              borderLeftColor: '#808080',
              borderRightColor: '#ffffff',
              borderBottomColor: '#ffffff'
            }}>
              file:///C:/Projects/{projectId}.html
            </div>
            <button
              className="px-2 py-1 text-xs bg-gradient-to-b from-gray-100 to-gray-200 border border-gray-400 hover:from-gray-200 hover:to-gray-300 active:from-gray-300 active:to-gray-200 transition-all"
              style={{
                borderTopColor: '#ffffff',
                borderLeftColor: '#ffffff',
                borderRightColor: '#808080',
                borderBottomColor: '#808080'
              }}
            >
              Go
            </button>
          </div>
        </div>
      </div>

      {/* Content Area with Windows XP styling */}
      <div className="flex-1 overflow-auto bg-white">
        <div 
          className="h-full w-full"
          style={{
            fontFamily: 'Tahoma, Arial, sans-serif',
          }}
        >
          <iframe
            srcDoc={htmlContent}
            className="w-full h-full border-0"
            title={`Project: ${projectId}`}
            sandbox="allow-same-origin allow-scripts"
          />
        </div>
      </div>

      {/* Status Bar */}
      <div className="bg-gradient-to-b from-gray-100 to-gray-200 border-t-2 border-gray-400 px-3 py-1 text-xs text-gray-700 flex items-center justify-between"
           style={{
             fontFamily: 'Tahoma, sans-serif',
             borderTopColor: '#ffffff'
           }}>
        <div className="flex items-center gap-4">
          <span>✅ Done</span>
          <span>📄 Document: Complete</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="bg-white border border-gray-400 px-2 py-0.5 text-xs" style={{
            borderTopColor: '#808080',
            borderLeftColor: '#808080',
            borderRightColor: '#ffffff',
            borderBottomColor: '#ffffff'
          }}>
            🌐 Internet Zone
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectViewer;
