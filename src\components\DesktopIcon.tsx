
import { useState, useRef, useCallback } from 'react';
import { useIsMobile } from '../hooks/use-mobile';

interface DesktopIconProps {
  icon: {
    id: string;
    name: string;
    icon: string;
    type: string;
  };
  onDoubleClick: () => void;
}

const DesktopIcon = ({ icon, onDoubleClick }: DesktopIconProps) => {
  const [isSelected, setIsSelected] = useState(false);
  const [clickCount, setClickCount] = useState(0);
  const isMobile = useIsMobile();
  const clickTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const selectionTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleInteraction = useCallback(() => {
    setIsSelected(true);

    // Clear existing timeouts
    if (clickTimeoutRef.current) {
      clearTimeout(clickTimeoutRef.current);
    }
    if (selectionTimeoutRef.current) {
      clearTimeout(selectionTimeoutRef.current);
    }

    if (isMobile) {
      // On mobile, single tap opens the window immediately
      onDoubleClick();
      setIsSelected(false);
      return;
    }

    // Desktop behavior: handle double-click
    setClickCount(prev => {
      const newCount = prev + 1;

      if (newCount === 1) {
        // First click - set timeout to reset
        clickTimeoutRef.current = setTimeout(() => {
          setClickCount(0);
          setIsSelected(false);
        }, 300);
      } else if (newCount === 2) {
        // Second click - trigger double click
        onDoubleClick();
        setClickCount(0);
        setIsSelected(false);
      }

      return newCount;
    });
  }, [isMobile, onDoubleClick]);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    // Prevent default to avoid triggering click events on mobile
    e.preventDefault();
    handleInteraction();
  }, [handleInteraction]);

  const handleClick = useCallback((e: React.MouseEvent) => {
    // Only handle click events on desktop
    if (!isMobile) {
      handleInteraction();
    }
  }, [isMobile, handleInteraction]);

  return (
    <div
      className={`flex flex-col items-center w-20 p-2 cursor-pointer transition-all select-none ${
        isSelected ? 'bg-blue-500 bg-opacity-30' : ''
      }`}
      style={{ outline: 'none', border: 'none' }}
      onClick={handleClick}
      onTouchStart={handleTouchStart}
    >
      <div
        className="h-10 mb-1 flex items-center justify-center"
      >
        <span className="text-3xl drop-shadow-lg">{icon.icon}</span>
      </div>
      <div className="text-white text-xs text-center leading-tight font-medium drop-shadow-lg">
        {icon.name}
      </div>
    </div>
  );
};

export default DesktopIcon;
