
import React, { useState, useEffect } from 'react';
import { X, Shield } from 'lucide-react';
import { Button } from './ui/button';
import { Progress } from './ui/progress';

const Antivirus = ({ onClose }: { onClose?: () => void }) => {
    const [isScanning, setIsScanning] = useState(false);
    const [progress, setProgress] = useState(0);
    const [scanComplete, setScanComplete] = useState(false);

    useEffect(() => {
        let timer: NodeJS.Timeout;
        if (isScanning && progress < 100) {
            timer = setTimeout(() => {
                setProgress(prev => Math.min(100, prev + 1));
            }, 50);
        } else if (isScanning && progress >= 100) {
            setIsScanning(false);
            setScanComplete(true);
        }
        return () => clearTimeout(timer);
    }, [isScanning, progress]);

    const handleScan = () => {
        setProgress(0);
        setScanComplete(false);
        setIsScanning(true);
    };

    return (
        <div
            className="w-[95vw] max-w-md bg-[#ECE9D8] border border-gray-400 rounded-sm p-1 font-tahoma text-xs shadow-lg text-black"
            style={{
                fontFamily: 'Tahoma, sans-serif',
                boxShadow: '2px 2px 4px rgba(0,0,0,0.5), inset 1px 1px 0px #FFF, inset -1px -1px 0px #848278',
                borderTop: '1px solid #FFFFFF',
                borderLeft: '1px solid #FFFFFF',
                borderRightColor: '#A0A0A0',
                borderBottomColor: '#A0A0A0',
                background: '#DFDDCF'
            }}
            onClick={(e) => e.stopPropagation()}
        >
            <div className="flex items-center justify-between mb-2 px-1 py-0.5 bg-gradient-to-r from-[#0058E0] to-[#3891F8]">
                <div className="flex items-center gap-1">
                    <Shield size={14} className="text-white" />
                    <span className="font-bold text-white select-none">Security Suite - Code Guardian</span>
                </div>
                {onClose && (
                    <button
                        onClick={onClose}
                        className="bg-[#E2614A] hover:bg-[#F0715A] w-4 h-4 flex items-center justify-center rounded-sm"
                        style={{
                            borderTop: '1px solid #FFF',
                            borderLeft: '1px solid #FFF',
                            borderRight: '1px solid #000',
                            borderBottom: '1px solid #000',
                        }}
                    >
                        <X size={10} className="text-white" strokeWidth={2.5} />
                    </button>
                )}
            </div>
            
            <div className="p-2 grid grid-cols-1 md:grid-cols-2 gap-2">
                <div className="space-y-2">
                    <div className="border p-2 bg-gray-50 rounded-sm h-full">
                        <h3 className="font-bold mb-2 flex items-center gap-1"><Shield className="text-green-600" size={16} /> System Protection Status</h3>
                        <p className="text-green-700 font-semibold">Code quality: Protected</p>
                        <p className="text-xs text-gray-600 mt-1">Your codebase is clean and follows best practices. No legacy code detected.</p>
                    </div>
                </div>

                <div className="space-y-2">
                     <div className="border p-2 bg-gray-50 rounded-sm h-full flex flex-col">
                        <h3 className="font-bold mb-2">Full System Scan</h3>
                        <div className="flex-grow">
                            {!isScanning && !scanComplete && <p className="text-xs text-gray-600 mb-2">Scan through portfolio to find threats and bugs.</p>}
                            
                            {isScanning && (
                                <div className="space-y-2">
                                    <Progress value={progress} className="h-4" />
                                    <p className="text-xs text-center">Scanning... {progress}%</p>
                                    <p className="text-xs text-gray-500 truncate">Checking: src/components/...</p>
                                </div>
                            )}

                            {scanComplete && (
                                <div className="text-center p-2 bg-green-100 rounded">
                                    <Shield className="text-green-600 mx-auto" size={24} />
                                    <p className="font-bold text-green-700 mt-1">Scan Complete</p>
                                    <p className="text-xs">System clean - developer ready for deployment!</p>
                                </div>
                            )}
                        </div>
                        <Button onClick={handleScan} disabled={isScanning} className="w-full mt-2" size="sm" variant="secondary">
                            {isScanning ? 'Scanning...' : 'Start Full Scan'}
                        </Button>
                    </div>
                </div>
            </div>

            <div className="border-t mt-2 p-2 bg-gray-50 rounded-sm">
                <h3 className="font-bold mb-2">Threat Detection</h3>
                <div className="text-xs space-y-1">
                    <p>Real-time scanning: <span className="text-green-600 font-semibold">Active</span></p>
                    <p>Quarantined items: 0</p>
                    <p>Security vulnerabilities: <span className="font-bold text-green-600">0 found</span></p>
                </div>
            </div>

            <div className="border-t p-1 text-center text-[10px] text-gray-500">
                Security definitions updated with latest best practices.
            </div>
        </div>
    );
};

export default Antivirus;
