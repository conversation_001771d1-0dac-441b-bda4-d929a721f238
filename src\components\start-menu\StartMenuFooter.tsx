
import { LogOut, Power } from 'lucide-react';
import { ShutdownType } from '../../hooks/useShutdown';

interface StartMenuFooterProps {
    onClose: () => void;
    onShutdown: (type: ShutdownType) => void;
}

const StartMenuFooter = ({ onClose, onShutdown }: StartMenuFooterProps) => (
    <div className="bg-gradient-to-r from-[#2155C4] to-[#4D90FE] p-1 flex justify-end items-center space-x-2 border-t border-t-[#769bce]">
        <button
            onClick={() => {
                onShutdown('logoff');
                onClose();
            }}
            className="flex items-center space-x-2 text-white hover:opacity-80 px-2 py-1 rounded-sm text-sm transition-opacity"
        >
            <LogOut size={20} />
            <span>Log Off</span>
        </button>
        <button
            className="flex items-center space-x-2 text-white hover:opacity-80 px-2 py-1 rounded-sm text-sm transition-opacity"
            onClick={() => {
                onShutdown('shutdown');
                onClose();
            }}
        >
            <Power size={20} />
            <span>Turn Off</span>
        </button>
    </div>
);

export default StartMenuFooter;
