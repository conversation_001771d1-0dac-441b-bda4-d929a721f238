
import { Settings } from 'lucide-react';

const ControlPanelContent = () => {
  return (
    <div className="p-4 bg-white font-tahoma h-full">
      <div className="flex items-center mb-4 pb-2 border-b-2 border-blue-500">
        <Settings size={32} className="text-gray-600 mr-3" />
        <h2 className="text-xl font-bold text-gray-800">Control Panel</h2>
      </div>
      <p className="text-gray-700">
        This is a placeholder for settings and preferences. In a real application, you could control theme settings, accessibility options, or other application-wide configurations here.
      </p>
    </div>
  );
};

export default ControlPanelContent;
