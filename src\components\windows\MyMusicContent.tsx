
import { Play, Pause, StopCircle } from 'lucide-react';
import { useEffect, useRef } from 'react';

const MyMusicContent = () => {
  const playerRef = useRef<any>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    // This function will be called by the YouTube API script once it's loaded
    (window as any).onYouTubeIframeAPIReady = () => {
      if (iframeRef.current) {
        playerRef.current = new (window as any).YT.Player(iframeRef.current, {
          events: {
            // Can add events here like onReady or onStateChange if needed later
          },
        });
      }
    };

    // Check if the YouTube IFrame API script is already loaded
    if ((window as any).YT && (window as any).YT.Player) {
      (window as any).onYouTubeIframeAPIReady();
    } else {
      const tag = document.createElement('script');
      tag.src = "https://www.youtube.com/iframe_api";
      const firstScriptTag = document.getElementsByTagName('script')[0];
      if (firstScriptTag && firstScriptTag.parentNode) {
        firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);
      } else {
        document.head.appendChild(tag);
      }
    }

    return () => {
      // Clean up the global function and player instance
      if (playerRef.current && typeof playerRef.current.destroy === 'function') {
        playerRef.current.destroy();
      }
      (window as any).onYouTubeIframeAPIReady = null;
    };
  }, []);

  const handlePlay = () => {
    playerRef.current?.playVideo();
  };

  const handlePause = () => {
    playerRef.current?.pauseVideo();
  };

  const handleStop = () => {
    playerRef.current?.stopVideo();
  };

  return (
    <div className="bg-[#ECE9D8] font-tahoma text-sm h-full flex flex-col">
      {/* Fake WMP UI */}
      <div className="flex-1 bg-black flex items-center justify-center">
        <iframe
          ref={iframeRef}
          width="100%"
          height="100%"
          src="https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=1&mute=1&controls=0&enablejsapi=1"
          title="YouTube video player"
          frameBorder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
        ></iframe>
      </div>
      <div className="bg-[#ECE9D8] p-2 border-t-2 border-white flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <button className="xp-button" onClick={handlePlay}><Play size={20} /></button>
          <button className="xp-button" onClick={handlePause}><Pause size={20} /></button>
          <button className="xp-button" onClick={handleStop}><StopCircle size={20} /></button>
        </div>
        <div className="w-1/2 h-4 bg-gray-300 border border-gray-400 rounded-full overflow-hidden">
          <div className="bg-blue-500 h-full w-1/3"></div>
        </div>
      </div>
      <div className="text-xs text-center py-1 border-t border-gray-400">
        Ready
      </div>
    </div>
  );
};

export default MyMusicContent;
