import { useState, useEffect } from 'react';
import { Popover, PopoverTrigger, PopoverContent } from './ui/popover';
import VolumeMixer from './VolumeMixer';
import { Volume2, Network, Shield } from 'lucide-react';
import LanStatus from './LanStatus';
import Antivirus from './Antivirus';

interface TaskbarProps {
  openWindows: Array<{
    id: string;
    title: string;
    isMinimized: boolean;
  }>;
  onStartClick: () => void;
  onWindowClick: (windowId: string) => void;
  onOpenWindow: (iconId: string, iconName: string) => void;
}

const Taskbar = ({ openWindows, onStartClick, onWindowClick, onOpenWindow }: TaskbarProps) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [mixerOpen, setMixerOpen] = useState(false);
  const [lanStatusOpen, setLanStatusOpen] = useState(false);
  const [antivirusOpen, setAntivirusOpen] = useState(false);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour12: true,
      hour: 'numeric',
      minute: '2-digit'
    });
  };

  return (
    <div className="fixed bottom-0 left-0 right-0 h-10 xp-taskbar flex items-center z-50">
      {/* Start Button */}
      <button
        className="h-full px-4 text-sm flex items-center space-x-2 font-bold text-white relative overflow-hidden group transition-all duration-75 xp-start-button-realistic"
        onClick={(e) => {
          e.stopPropagation();
          onStartClick();
        }}
        style={{
          background: 'linear-gradient(to bottom, #5cb85c 0%, #4cae4c 50%, #3e8e3e 100%)',
          border: '1px outset #5cb85c',
          borderRadius: '0 8px 8px 0',
          boxShadow: 'inset 1px 1px 2px rgba(255,255,255,0.4), inset -1px -1px 2px rgba(0,0,0,0.2), 2px 2px 4px rgba(0,0,0,0.3)',
        }}
        onMouseDown={(e) => {
          e.currentTarget.style.border = '1px inset #5cb85c';
          e.currentTarget.style.boxShadow = 'inset -1px -1px 2px rgba(255,255,255,0.4), inset 1px 1px 2px rgba(0,0,0,0.3)';
        }}
        onMouseUp={(e) => {
          e.currentTarget.style.border = '1px outset #5cb85c';
          e.currentTarget.style.boxShadow = 'inset 1px 1px 2px rgba(255,255,255,0.4), inset -1px -1px 2px rgba(0,0,0,0.2), 2px 2px 4px rgba(0,0,0,0.3)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.border = '1px outset #5cb85c';
          e.currentTarget.style.boxShadow = 'inset 1px 1px 2px rgba(255,255,255,0.4), inset -1px -1px 2px rgba(0,0,0,0.2), 2px 2px 4px rgba(0,0,0,0.3)';
        }}
      >
        {/* Windows Logo */}
        <div className="w-6 h-6 flex items-center justify-center">
          <img
            src="/windows-logo.png"
            alt="Windows XP Logo"
            className="w-5 h-5 drop-shadow-sm"
          />
        </div>
        
        {/* Start text */}
        <span 
          className="text-white font-bold drop-shadow-sm select-none"
          style={{
            fontFamily: 'Tahoma, sans-serif',
            fontSize: '13px',
            textShadow: '1px 1px 1px rgba(0,0,0,0.5)'
          }}
        >
          start
        </span>

        {/* Hover glow effect */}
        <div 
          className="absolute inset-0 opacity-0 group-hover:opacity-20 transition-opacity duration-150 pointer-events-none"
          style={{
            background: 'linear-gradient(to bottom, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.1) 50%, rgba(255,255,255,0) 100%)',
            borderRadius: '0 8px 8px 0',
          }}
        />
      </button>

      {/* Quick Launch Area */}
      <div className="flex items-center space-x-1 ml-2 pl-2 border-l border-blue-400">
        <button
          className="p-1 hover:bg-blue-400 active:bg-blue-500 rounded transition-colors"
          title="My Projects"
          onClick={() => onOpenWindow('projects', 'My Projects')}
          onTouchStart={(e) => {
            e.currentTarget.style.backgroundColor = '#60a5fa';
          }}
          onTouchEnd={(e) => {
            e.currentTarget.style.backgroundColor = '';
          }}
        >
          <span className="text-lg">📁</span>
        </button>
        <button
          className="p-1 hover:bg-blue-400 active:bg-blue-500 rounded transition-colors"
          title="Contact Info"
          onClick={() => onOpenWindow('contact', 'Contact Info')}
          onTouchStart={(e) => {
            e.currentTarget.style.backgroundColor = '#60a5fa';
          }}
          onTouchEnd={(e) => {
            e.currentTarget.style.backgroundColor = '';
          }}
        >
          <span className="text-lg">📧</span>
        </button>
      </div>

      {/* Open Windows */}
      <div className="flex-1 flex items-center space-x-1 ml-2 h-full">
        {openWindows.map((window) => (
          <button
            key={window.id}
            className={`h-full flex items-center px-2 sm:px-3 text-xs sm:text-sm text-white rounded border ${
              window.isMinimized
                ? 'bg-blue-600 border-blue-500'
                : 'bg-blue-500 border-blue-400'
            } hover:bg-blue-400 active:bg-blue-300 transition-colors max-w-24 sm:max-w-40 truncate`}
            onClick={() => onWindowClick(window.id)}
            onTouchStart={(e) => {
              e.currentTarget.style.backgroundColor = '#60a5fa';
            }}
            onTouchEnd={(e) => {
              e.currentTarget.style.backgroundColor = '';
            }}
            title={window.title}
          >
            {window.title}
          </button>
        ))}
      </div>

      {/* System Tray */}
      <div className="flex items-center space-x-1 sm:space-x-2 mr-2 border-l border-blue-400 pl-1 sm:pl-2">
        {/* System Icons */}
        <div className="flex items-center space-x-1">
          <Popover open={mixerOpen} onOpenChange={setMixerOpen}>
            <PopoverTrigger asChild>
              <button className="text-white cursor-pointer hover:bg-blue-400 p-1 rounded focus:outline-none">
                <Volume2 size={16} />
              </button>
            </PopoverTrigger>
            <PopoverContent 
              className="w-auto p-0 border-none bg-transparent shadow-none mb-1" 
              side="top" 
              align="end"
              sideOffset={5}
            >
              <VolumeMixer onClose={() => setMixerOpen(false)} />
            </PopoverContent>
          </Popover>
          <Popover open={lanStatusOpen} onOpenChange={setLanStatusOpen}>
            <PopoverTrigger asChild>
              <button className="text-white cursor-pointer hover:bg-blue-400 p-1 rounded focus:outline-none">
                <Network size={16} />
              </button>
            </PopoverTrigger>
            <PopoverContent 
              className="w-auto p-0 border-none bg-transparent shadow-none mb-1" 
              side="top" 
              align="end"
              sideOffset={5}
            >
              <LanStatus onClose={() => setLanStatusOpen(false)} />
            </PopoverContent>
          </Popover>
          <Popover open={antivirusOpen} onOpenChange={setAntivirusOpen}>
            <PopoverTrigger asChild>
              <button className="text-white cursor-pointer hover:bg-blue-400 p-1 rounded focus:outline-none">
                <Shield size={16} />
              </button>
            </PopoverTrigger>
            <PopoverContent 
              className="w-auto p-0 border-none bg-transparent shadow-none mb-1" 
              side="top" 
              align="end"
              sideOffset={5}
            >
              <Antivirus onClose={() => setAntivirusOpen(false)} />
            </PopoverContent>
          </Popover>
        </div>
        
        {/* Clock */}
        <div className="text-white text-xs font-medium">
          {formatTime(currentTime)}
        </div>
      </div>
    </div>
  );
};

export default Taskbar;
