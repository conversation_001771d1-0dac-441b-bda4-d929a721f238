
import { FileIcon } from '../../../lib/file-icons';
import { RecycleBinFile } from './recycleBinData';

interface RecycleBinFileViewerProps {
  file: RecycleBinFile;
}

const RecycleBinFileViewer = ({ file }: RecycleBinFileViewerProps) => {
  return (
    <>
      <div className="bg-white border-b border-gray-300 p-3">
        <div className="flex items-center space-x-3">
          <FileIcon filename={file.name} className="h-6 w-6 text-gray-800" />
          <div>
            <h2 className="font-bold text-gray-800">{file.name}</h2>
            <p className="text-sm text-gray-600">
              {file.type} • {file.size} • Modified {file.modified}
            </p>
          </div>
        </div>
      </div>
      
      <div className="flex-1 overflow-auto p-4">
        <div className="bg-white rounded border border-gray-300 p-4 font-mono text-sm whitespace-pre-line">
          {file.content}
        </div>
      </div>
    </>
  );
};

export default RecycleBinFileViewer;
