
import { useState } from 'react';
import Taskbar from './Taskbar';
import DesktopIcon from './DesktopIcon';
import Window from './Window';
import StartMenu from './StartMenu';
import ShutdownSequence from './ShutdownSequence';
import { useShutdown } from '../hooks/useShutdown';
import { desktopIcons, portfolioItems } from '../lib/constants';
import { useIsMobile } from '../hooks/use-mobile';
import WelcomeDialog from './WelcomeDialog';
import { useWindowManager } from '../hooks/useWindowManager';
import windowsXpBliss from '../assets/windows-xp-bliss.jpg';

const Desktop = () => {
  const [showStartMenu, setShowStartMenu] = useState(false);
  const [showWelcomeDialog, setShowWelcomeDialog] = useState(true);
  const { isShuttingDown, shutdownType, triggerShutdown, resetToDesktop } = useShutdown();
  const isMobile = useIsMobile();
  const {
    openWindows,
    openWindow,
    closeWindow,
    minimizeWindow,
    maximizeWindow,
    restoreWindow,
    bringToFront,
    updateWindowPosition,
    updateWindowSize,
    handleTaskbarClick,
  } = useWindowManager(isMobile);

  if (isShuttingDown) {
    return <ShutdownSequence type={shutdownType} onComplete={resetToDesktop} />;
  }

  return (
    <div 
      className="h-screen w-screen relative bg-cover bg-center bg-no-repeat"
      style={{
        backgroundImage: `url(${windowsXpBliss})`
      }}
      onClick={() => setShowStartMenu(false)}
    >
      {/* Desktop Icons */}
      <div className="absolute top-0 left-0 right-0 bottom-10 p-4 overflow-hidden">
        <div className="flex flex-col flex-wrap content-start h-full gap-y-4">
          {desktopIcons.map((icon) => (
            <DesktopIcon
              key={icon.id}
              icon={icon}
              onDoubleClick={() => openWindow(icon.id, icon.name)}
            />
          ))}
        </div>
      </div>

      {/* Windows */}
      {openWindows.map((window) => (
        !window.isMinimized && (
          <Window
            key={window.id}
            window={window}
            onClose={() => closeWindow(window.id)}
            onMinimize={() => minimizeWindow(window.id)}
            onMaximize={() => maximizeWindow(window.id)}
            onRestore={() => restoreWindow(window.id)}
            onBringToFront={() => bringToFront(window.id)}
            onUpdatePosition={(position) => updateWindowPosition(window.id, position)}
            onUpdateSize={(size) => updateWindowSize(window.id, size)}
            isMobile={isMobile}
          />
        )
      ))}

      {/* Welcome Dialog */}
      {showWelcomeDialog && (
        <WelcomeDialog
          onClose={() => setShowWelcomeDialog(false)}
          onOpenWindow={openWindow}
        />
      )}

      {/* Start Menu */}
      {showStartMenu && (
        <StartMenu 
          menuItems={portfolioItems}
          onClose={() => setShowStartMenu(false)}
          onOpenWindow={openWindow}
          onShutdown={triggerShutdown}
        />
      )}

      {/* Taskbar */}
      <Taskbar
        openWindows={openWindows}
        onStartClick={() => setShowStartMenu(!showStartMenu)}
        onOpenWindow={openWindow}
        onWindowClick={handleTaskbarClick}
      />
    </div>
  );
};

export default Desktop;
