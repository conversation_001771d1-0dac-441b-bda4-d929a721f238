<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chromepanion - Browser Extension</title>
    <style>
        body {
            font-family: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
            margin: 0;
            padding: 20px;
            background: #316AC5;
            color: black;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #ECE9D8;
            padding: 20px;
            border: 2px outset #ECE9D8;
            box-shadow: 2px 2px 5px #808080;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 2em;
            color: #000080;
            text-decoration: underline;
        }
        .project-info {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .info-card {
            background: #F0F0F0;
            padding: 15px;
            border: 2px inset #ECE9D8;
            margin-bottom: 15px;
        }
        .tech-stack {
            margin-top: 10px;
        }
        .tech-tag {
            background: #DBEAFE;
            padding: 3px 8px;
            border: 1px solid #3B82F6;
            font-size: 0.8em;
            margin: 2px;
            display: inline-block;
        }
        .features {
            list-style: none;
            padding: 0;
            margin: 10px 0;
        }
        .features li {
            padding: 5px 0;
            border-bottom: 1px solid #C0C0C0;
        }
        .features li:before {
            content: "• ";
            color: #000080;
            font-weight: bold;
        }
        .back-btn {
            background: #ECE9D8;
            color: black;
            border: 2px outset #ECE9D8;
            padding: 5px 15px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 15px;
            font-family: Tahoma, Arial, sans-serif;
        }
        .back-btn:hover {
            background: #D4D0C8;
        }
        h3 {
            color: #000080;
            font-size: 1.1em;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Chromepanion Browser Extension</h1>
        <p style="text-align: center; font-size: 1.2em; margin-bottom: 30px;">
            Your intelligent browsing companion that enhances productivity and provides smart assistance while you browse.
        </p>
        
        <div class="project-info">
            <div class="info-card">
                <h3>🛠️ Technology Stack</h3>
                <div class="tech-stack">
                    <span class="tech-tag">TypeScript</span>
                    <span class="tech-tag">Chrome Extension API</span>
                    <span class="tech-tag">React</span>
                    <span class="tech-tag">Webpack</span>
                    <span class="tech-tag">AI/ML APIs</span>
                </div>
            </div>
            
            <div class="info-card">
                <h3>📊 Project Status</h3>
                <p><strong>Status:</strong> Beta Testing</p>
                <p><strong>Version:</strong> 2.1.0</p>
                <p><strong>Users:</strong> 1,200+ beta testers</p>
                <p><strong>Rating:</strong> 4.9/5 stars</p>
            </div>
        </div>
        
        <div class="info-card">
            <h3>✨ Key Features</h3>
            <ul class="features">
                <li>AI-powered content summarization and analysis</li>
                <li>Smart bookmark organization with auto-tagging</li>
                <li>Real-time language translation overlay</li>
                <li>Password security analysis and suggestions</li>
                <li>Tab management with intelligent grouping</li>
                <li>Privacy protection and tracker blocking</li>
                <li>Custom shortcuts and automation workflows</li>
            </ul>
        </div>
        
        <div class="info-card">
            <h3>📝 Description</h3>
            <p>
                Chromepanion is an advanced browser extension that acts as your personal browsing assistant. 
                Using cutting-edge AI technology, it provides intelligent insights, automates repetitive tasks, 
                and enhances your overall browsing experience.
            </p>
            <p>
                The extension learns from your browsing patterns to offer personalized suggestions, helps you 
                stay organized with smart bookmark management, and protects your privacy while you explore the web. 
                Perfect for power users who want to maximize their productivity online.
            </p>
        </div>
        
        <a href="javascript:history.back()" class="back-btn">← Back to Projects</a>
    </div>
</body>
</html>
