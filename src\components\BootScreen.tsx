
import { useState, useEffect } from 'react';

interface BootScreenProps {
  onBootComplete: () => void;
}

const BootScreen = ({ onBootComplete }: BootScreenProps) => {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    // Boot progress animation
    const interval = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval);
          // Go directly to desktop after boot completes
          setTimeout(() => {
            onBootComplete();
          }, 500);
          return 100;
        }
        return prev + 2;
      });
    }, 20);

    return () => clearInterval(interval);
  }, [onBootComplete]);

  return (
    <div className="h-screen w-screen bg-black flex flex-col items-center justify-center text-white">
      {/* Windows XP Logo */}
      <div className="mb-16 flex flex-col items-center">
        {/* XP Flag Logo */}
        <div className="flex mb-4">
          <div className="w-8 h-12 bg-red-500 rounded-l-lg"></div>
          <div className="w-8 h-12 bg-orange-500"></div>
          <div className="w-8 h-12 bg-blue-500"></div>
          <div className="w-8 h-12 bg-green-500 rounded-r-lg"></div>
        </div>
        
        {/* Microsoft Windows XP Text */}
        <div className="text-center">
          <div className="text-lg text-gray-300 mb-1">Microsoft</div>
          <div className="text-3xl font-bold text-white mb-1">Windows <span className="text-red-500">XP</span></div>
          <div className="text-sm text-gray-400">Professional</div>
        </div>
      </div>
      
      {/* Loading Bar */}
      <div className="w-64 mb-8">
        <div className="bg-gray-800 h-4 rounded-sm border border-gray-600 overflow-hidden">
          <div 
            className="h-full bg-gradient-to-r from-blue-400 via-blue-500 to-blue-600 transition-all duration-100 ease-out relative"
            style={{ width: `${progress}%` }}
          >
            {/* Animated shine effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-pulse"></div>
          </div>
        </div>
      </div>
      
      {/* Loading Text */}
      <div className="text-sm text-gray-400">
        {progress < 30 && "Loading Windows..."}
        {progress >= 30 && progress < 60 && "Starting services..."}
        {progress >= 60 && progress < 90 && "Preparing desktop..."}
        {progress >= 90 && "Welcome"}
      </div>
    </div>
  );
};

export default BootScreen;
