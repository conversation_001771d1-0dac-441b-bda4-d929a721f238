
import { Image as ImageIcon } from 'lucide-react';

const pictures = [
  {
    src: 'https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=600&q=80',
    alt: 'Project Demo 1: A MacBook with lines of code on its screen',
    title: 'Development Environment.jpg',
    size: '1.2 MB'
  },
  {
    src: 'https://images.unsplash.com/photo-1526374965328-7f61d4dc18c5?w=600&q=80',
    alt: 'Programming meme: Matrix movie still with code',
    title: 'The Matrix of Code.gif',
    size: '2.5 MB'
  },
  {
    src: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=600&q=80',
    alt: 'Project Screenshot: Java code on a monitor',
    title: 'LegacySystemRefactor.png',
    size: '850 KB'
  },
  {
    src: 'https://images.unsplash.com/photo-1487058792275-0ad4aaf24ca7?w=600&q=80',
    alt: 'Project Screenshot: Colorful web code on a screen',
    title: 'RainbowSyntax.png',
    size: '970 KB'
  },
];

const MyPicturesContent = () => {
  return (
    <div className="p-4 bg-white font-tahoma h-full overflow-y-auto">
      <div className="flex items-center mb-4 pb-2 border-b-2 border-blue-500">
        <ImageIcon size={32} className="text-blue-600 mr-3" />
        <h2 className="text-xl font-bold text-blue-800">My Pictures</h2>
      </div>
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {pictures.map((pic, index) => (
          <div key={index} className="group cursor-pointer">
            <div className="bg-gray-100 border rounded overflow-hidden aspect-square flex items-center justify-center group-hover:shadow-lg transition-shadow">
              <img src={pic.src} alt={pic.alt} className="w-full h-full object-cover" />
            </div>
            <div className="text-center mt-2 text-xs">
              <p className="font-semibold truncate">{pic.title}</p>
              <p className="text-gray-500">{pic.size}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MyPicturesContent;
