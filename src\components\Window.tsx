
import { useRef, memo } from 'react';
import { useWindowDrag } from '../hooks/useWindowDrag';
import { useWindowResize } from '../hooks/useWindowResize';

interface WindowProps {
  window: {
    id: string;
    title: string;
    content: string | React.ReactNode;
    icon: string;
    position: { x: number; y: number };
    size: { width: number; height: number };
    zIndex: number;
    isMaximized?: boolean;
    originalPosition?: { x: number; y: number };
    originalSize?: { width: number; height: number };
  };
  onClose: () => void;
  onMinimize: () => void;
  onMaximize: () => void;
  onBringToFront: () => void;
  onUpdatePosition: (position: { x: number; y: number }) => void;
  onUpdateSize?: (size: { width: number; height: number }) => void;
  onRestore?: () => void;
  isMobile?: boolean;
}

const WindowComponent = ({ 
  window: windowData, 
  onClose, 
  onMinimize, 
  onMaximize, 
  onBringToFront, 
  onUpdatePosition,
  onUpdateSize,
  onRestore,
  isMobile
}: WindowProps) => {
  const windowRef = useRef<HTMLDivElement>(null);
  
  const { handleMouseDown } = useWindowDrag({
    windowData,
    onUpdatePosition,
    onBringToFront,
    isMobile,
  });

  const { handleResizeStart } = useWindowResize({
    windowData,
    onUpdatePosition,
    onUpdateSize,
    onBringToFront,
    isMobile,
  });

  const handleMaximizeRestore = () => {
    if (windowData.isMaximized && onRestore) {
      onRestore();
    } else {
      onMaximize();
    }
  };

  const isReactContent = typeof windowData.content !== 'string';

  const getCursorStyle = (direction: string) => {
    const cursorMap: { [key: string]: string } = {
      'top': 'n-resize',
      'bottom': 's-resize',
      'left': 'w-resize',
      'right': 'e-resize',
      'top-left': 'nw-resize',
      'top-right': 'ne-resize',
      'bottom-left': 'sw-resize',
      'bottom-right': 'se-resize'
    };
    return cursorMap[direction] || 'default';
  };

  return (
    <div
      ref={windowRef}
      className="absolute xp-window bg-gray-100 animate-window-open"
      style={{
        left: windowData.position.x,
        top: windowData.position.y,
        width: windowData.size.width,
        height: windowData.size.height,
        zIndex: windowData.zIndex,
      }}
      onMouseDown={onBringToFront}
    >
      {/* Resize handles - only show when not maximized */}
      {!windowData.isMaximized && !isMobile && (
        <>
          {/* Corner handles */}
          <div
            className="resize-handle absolute top-0 left-0 w-2 h-2 z-10"
            style={{ cursor: getCursorStyle('top-left') }}
            onMouseDown={(e) => handleResizeStart(e, 'top-left')}
          />
          <div
            className="resize-handle absolute top-0 right-0 w-2 h-2 z-10"
            style={{ cursor: getCursorStyle('top-right') }}
            onMouseDown={(e) => handleResizeStart(e, 'top-right')}
          />
          <div
            className="resize-handle absolute bottom-0 left-0 w-2 h-2 z-10"
            style={{ cursor: getCursorStyle('bottom-left') }}
            onMouseDown={(e) => handleResizeStart(e, 'bottom-left')}
          />
          <div
            className="resize-handle absolute bottom-0 right-0 w-2 h-2 z-10"
            style={{ cursor: getCursorStyle('bottom-right') }}
            onMouseDown={(e) => handleResizeStart(e, 'bottom-right')}
          />
          
          {/* Edge handles */}
          <div
            className="resize-handle absolute top-0 left-2 right-2 h-1 z-10"
            style={{ cursor: getCursorStyle('top') }}
            onMouseDown={(e) => handleResizeStart(e, 'top')}
          />
          <div
            className="resize-handle absolute bottom-0 left-2 right-2 h-1 z-10"
            style={{ cursor: getCursorStyle('bottom') }}
            onMouseDown={(e) => handleResizeStart(e, 'bottom')}
          />
          <div
            className="resize-handle absolute left-0 top-2 bottom-2 w-1 z-10"
            style={{ cursor: getCursorStyle('left') }}
            onMouseDown={(e) => handleResizeStart(e, 'left')}
          />
          <div
            className="resize-handle absolute right-0 top-2 bottom-2 w-1 z-10"
            style={{ cursor: getCursorStyle('right') }}
            onMouseDown={(e) => handleResizeStart(e, 'right')}
          />
        </>
      )}

      {/* Title Bar */}
      <div
        className={`xp-titlebar h-7 flex items-center justify-between px-2 select-none ${windowData.isMaximized || isMobile ? 'cursor-default' : 'cursor-move'}`}
        onMouseDown={handleMouseDown}
      >
        <div className="flex items-center space-x-2">
          <span className="text-white text-xs">{windowData.icon}</span>
          <span className="text-white text-sm font-medium">{windowData.title}</span>
        </div>
        
        <div className="window-controls flex items-center space-x-1">
          <button
            className="w-5 h-4 bg-gray-300 hover:bg-gray-400 border border-gray-500 text-xs flex items-center justify-center"
            onClick={onMinimize}
            title="Minimize"
          >
            _
          </button>
          {!isMobile && (
            <button
              className="w-5 h-4 bg-gray-300 hover:bg-gray-400 border border-gray-500 text-xs flex items-center justify-center"
              onClick={handleMaximizeRestore}
              title={windowData.isMaximized ? "Restore" : "Maximize"}
            >
              {windowData.isMaximized ? '❐' : '□'}
            </button>
          )}
          <button
            className="w-5 h-4 bg-red-500 hover:bg-red-600 border border-red-700 text-white text-xs flex items-center justify-center"
            onClick={onClose}
            title="Close"
          >
            ×
          </button>
        </div>
      </div>

      {/* Menu Bar */}
      {windowData.id !== 'my_resume' && (
        <div className="h-6 bg-gray-200 border-b border-gray-300 flex items-center px-2 text-xs">
          <span className="hover:bg-blue-500 hover:text-white px-2 py-1 cursor-pointer">File</span>
          <span className="hover:bg-blue-500 hover:text-white px-2 py-1 cursor-pointer">Edit</span>
          <span className="hover:bg-blue-500 hover:text-white px-2 py-1 cursor-pointer">View</span>
          <span className="hover:bg-blue-500 hover:text-white px-2 py-1 cursor-pointer">Help</span>
        </div>
      )}

      {/* Content Area */}
      <div 
        className="overflow-auto"
        style={{ 
          height: `calc(100% - ${windowData.id === 'my_resume' ? '1.75rem' : '3.25rem'})`
        }}
      >
        {isReactContent ? (
          windowData.content
        ) : (
          <div dangerouslySetInnerHTML={{ __html: windowData.content as string }} />
        )}
      </div>
    </div>
  );
};

export default memo(WindowComponent);
