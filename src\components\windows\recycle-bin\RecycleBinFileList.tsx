
import { FileIcon } from '../../../lib/file-icons';
import { RecycleBinFile } from './recycleBinData';

interface RecycleBinFileListProps {
  files: RecycleBinFile[];
  selectedFile: string | null;
  onFileSelect: (fileId: string) => void;
}

const RecycleBinFileList = ({ files, selectedFile, onFileSelect }: RecycleBinFileListProps) => {
  return (
    <div className="w-64 bg-white border-r border-gray-300 flex flex-col">
      <div className="flex-1 overflow-y-auto">
        {files.map((file) => (
          <div
            key={file.id}
            className={`p-2 border-b border-gray-200 cursor-pointer hover:bg-blue-50 ${
              selectedFile === file.id ? 'bg-blue-100 border-l-4 border-l-blue-500' : ''
            }`}
            onClick={() => onFileSelect(file.id)}
          >
            <div className="flex items-center space-x-2 mb-1">
              <FileIcon filename={file.name} className="h-5 w-5 text-gray-700 flex-shrink-0" />
              <span className="text-sm font-medium truncate">{file.name}</span>
            </div>
            <div className="text-xs text-gray-500 pl-7">
              <div>{file.type}</div>
              <div>{file.size} • {file.modified}</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RecycleBinFileList;
