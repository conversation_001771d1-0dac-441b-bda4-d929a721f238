
import React from 'react';

const RightMenuItem = ({ icon, name, onClick }: { icon: React.ReactNode; name: string; onClick?: () => void }) => (
  <button
    className="w-full flex items-center space-x-3 px-3 py-1 hover:bg-[#316AC5] hover:text-white text-left text-sm rounded-sm transition-colors duration-150 group"
    onClick={onClick}
  >
    <div className="w-6 h-6 flex items-center justify-center">{icon}</div>
    <span className="font-semibold text-black group-hover:text-white">{name}</span>
  </button>
);

export default RightMenuItem;
