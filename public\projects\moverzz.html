<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Moverzz - Rental Mobile App</title>
    <style>
        body {
            font-family: <PERSON>homa, <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background: #316AC5;
            color: black;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #ECE9D8;
            padding: 20px;
            border: 2px outset #ECE9D8;
            box-shadow: 2px 2px 5px #808080;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 2em;
            color: #000080;
            text-decoration: underline;
        }
        .project-info {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .info-card {
            background: #F0F0F0;
            padding: 15px;
            border: 2px inset #ECE9D8;
            margin-bottom: 15px;
        }
        .tech-stack {
            margin-top: 10px;
        }
        .tech-tag {
            background: #DBEAFE;
            padding: 3px 8px;
            border: 1px solid #3B82F6;
            font-size: 0.8em;
            margin: 2px;
            display: inline-block;
        }
        .features {
            list-style: none;
            padding: 0;
            margin: 10px 0;
        }
        .features li {
            padding: 5px 0;
            border-bottom: 1px solid #C0C0C0;
        }
        .features li:before {
            content: "• ";
            color: #000080;
            font-weight: bold;
        }
        .back-btn {
            background: #ECE9D8;
            color: black;
            border: 2px outset #ECE9D8;
            padding: 5px 15px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 15px;
            font-family: Tahoma, Arial, sans-serif;
        }
        .back-btn:hover {
            background: #D4D0C8;
        }
        h3 {
            color: #000080;
            font-size: 1.1em;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Moverzz Mobile Application</h1>
        <p style="text-align: center; font-size: 1.2em; margin-bottom: 30px;">
            The ultimate rental mobile app connecting people who need to move with trusted movers and equipment.
        </p>
        
        <div class="project-info">
            <div class="info-card">
                <h3>🛠️ Technology Stack</h3>
                <div class="tech-stack">
                    <span class="tech-tag">React Native</span>
                    <span class="tech-tag">Node.js</span>
                    <span class="tech-tag">MongoDB</span>
                    <span class="tech-tag">Express.js</span>
                    <span class="tech-tag">Socket.io</span>
                    <span class="tech-tag">Stripe API</span>
                </div>
            </div>
            
            <div class="info-card">
                <h3>📊 Project Status</h3>
                <p><strong>Status:</strong> Production</p>
                <p><strong>Version:</strong> 3.2.1</p>
                <p><strong>Downloads:</strong> 50K+ downloads</p>
                <p><strong>Rating:</strong> 4.7/5 stars</p>
            </div>
        </div>
        
        <div class="info-card">
            <h3>✨ Key Features</h3>
            <ul class="features">
                <li>Real-time mover tracking and GPS navigation</li>
                <li>Instant quotes and transparent pricing</li>
                <li>In-app messaging and video calls</li>
                <li>Secure payment processing and escrow</li>
                <li>Equipment rental marketplace</li>
                <li>Review and rating system</li>
                <li>Insurance coverage options</li>
                <li>Photo documentation and inventory tracking</li>
            </ul>
        </div>
        
        <div class="info-card">
            <h3>📝 Description</h3>
            <p>
                Moverzz revolutionizes the moving industry by connecting customers with verified, professional 
                movers through an intuitive mobile platform. Whether you're moving across town or across the 
                country, Moverzz makes the process seamless and stress-free.
            </p>
            <p>
                The app features real-time tracking, transparent pricing, secure payments, and comprehensive 
                insurance options. Users can browse mover profiles, read reviews, get instant quotes, and 
                manage their entire move from their smartphone. Available on both iOS and Android platforms.
            </p>
        </div>
        
        <a href="javascript:history.back()" class="back-btn">← Back to Projects</a>
    </div>
</body>
</html>
