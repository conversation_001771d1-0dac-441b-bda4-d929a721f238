
import { useState } from 'react';
import { ShutdownType } from '../hooks/useShutdown';
import StartMenuHeader from './start-menu/StartMenuHeader';
import StartMenuLeftColumn from './start-menu/StartMenuLeftColumn';
import StartMenuRightColumn from './start-menu/StartMenuRightColumn';
import StartMenuFooter from './start-menu/StartMenuFooter';

interface StartMenuProps {
  onClose: () => void;
  onOpenWindow: (iconId: string, iconName: string) => void;
  onShutdown: (type: ShutdownType) => void;
  menuItems: Array<{ id: string; name: string; icon: string }>;
}

const StartMenu = ({ onClose, onOpenWindow, onShutdown, menuItems }: StartMenuProps) => {
  const [showAllPrograms, setShowAllPrograms] = useState(false);
  const handleOpenWindow = (id: string, name: string) => {
    onOpenWindow(id, name);
    onClose();
  };
  
  return (
    <div
      className="fixed bottom-10 left-0 z-50 flex items-end"
    >
      <div className="relative">
        <div
          className={`w-[480px] border-2 border-t-[#769bce] border-l-[#769bce] border-r-[#183969] border-b-[#183969] shadow-2xl overflow-hidden ${showAllPrograms ? 'rounded-l-lg rounded-t-lg' : 'rounded-lg'}`}
          onClick={(e) => e.stopPropagation()}
          style={{ fontFamily: 'Tahoma, sans-serif' }}
        >
          <StartMenuHeader />
          <div className="flex">
            <StartMenuLeftColumn 
              menuItems={menuItems}
              handleOpenWindow={handleOpenWindow}
              showAllPrograms={showAllPrograms}
              setShowAllPrograms={setShowAllPrograms}
            />
            <StartMenuRightColumn handleOpenWindow={handleOpenWindow} />
          </div>
          <StartMenuFooter onClose={onClose} onShutdown={onShutdown} />
        </div>
      </div>
    </div>
  );
};

export default StartMenu;
