
import React from 'react';
import { Globe, Smartphone, Tv, Shield } from 'lucide-react';

const ProgramMenuItem = ({ icon, name, onClick }: { icon: React.ReactNode, name: string, onClick?: () => void }) => {
    return (
        <button
            onClick={onClick}
            className="w-full flex items-center justify-between space-x-3 px-2 py-1.5 hover:bg-[#316AC5] hover:text-white text-left text-sm rounded-sm transition-colors duration-150 group"
        >
            <div className="flex items-center space-x-2">
                <div className="w-6 h-6 flex items-center justify-center">{icon}</div>
                <span className="text-black group-hover:text-white">{name}</span>
            </div>
        </button>
    );
};

const AllProgramsMenu = () => {
    const openLink = (url: string) => {
        window.open(url, '_blank', 'noopener,noreferrer');
    };

    return (
        <div
            className="absolute bottom-0 left-full w-[240px] bg-white border-2 border-t-[#769bce] border-l-[#769bce] border-r-[#183969] border-b-[#183969] shadow-2xl rounded-tr-lg rounded-br-lg animate-in fade-in duration-150"
            style={{ fontFamily: 'Tahoma, sans-serif' }}
            onClick={(e) => e.stopPropagation()}
        >
             <div className="p-1 space-y-0.5">
                <ProgramMenuItem icon={<Globe size={20} className="text-blue-700"/>} name="Browser Extension" onClick={() => openLink('https://github.com/yourusername')} />
                <ProgramMenuItem icon={<Smartphone size={20} className="text-blue-700"/>} name="Mobile App" onClick={() => openLink('https://github.com/yourusername')} />
                <ProgramMenuItem icon={<Tv size={20} className="text-blue-700"/>} name="Smart TV App" onClick={() => openLink('https://github.com/yourusername')} />
                <div className="border-t border-gray-300 my-1"></div>
                <ProgramMenuItem icon={<Shield size={20} className="text-green-700"/>} name="Security Suite" onClick={() => openLink('https://github.com/yourusername')} />
             </div>
        </div>
    );
};

export default AllProgramsMenu;
