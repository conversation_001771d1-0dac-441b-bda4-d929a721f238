
import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			fontFamily: {
				'tahoma': ['Tahoma', 'sans-serif'],
				'ms-sans': ['MS Sans Serif', 'sans-serif'],
			},
			colors: {
				// Windows XP Luna theme colors
				'xp-blue': {
					50: '#e6f3ff',
					100: '#b3dbff',
					500: '#0078d4',
					600: '#004c87',
					700: '#003d6b',
				},
				'xp-green': {
					400: '#73d216',
					500: '#5cb85c',
					600: '#4cae4c',
				},
				'xp-gray': {
					100: '#f0f0f0',
					200: '#e0e0e0',
					300: '#c0c0c0',
					400: '#a0a0a0',
					500: '#808080',
					600: '#606060',
				},
				'xp-taskbar': '#245edb',
				'xp-start': '#2e8b57',
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			keyframes: {
				'boot-progress': {
					'0%': { width: '0%' },
					'100%': { width: '100%' }
				},
				'fade-in': {
					'0%': { opacity: '0' },
					'100%': { opacity: '1' }
				},
				'slide-up': {
					'0%': { transform: 'translateY(100%)', opacity: '0' },
					'100%': { transform: 'translateY(0)', opacity: '1' }
				},
				'window-open': {
					'0%': { transform: 'scale(0.8)', opacity: '0' },
					'100%': { transform: 'scale(1)', opacity: '1' }
				}
			},
			animation: {
				'boot-progress': 'boot-progress 3s ease-in-out',
				'fade-in': 'fade-in 0.5s ease-out',
				'slide-up': 'slide-up 0.3s ease-out',
				'window-open': 'window-open 0.2s ease-out'
			},
			boxShadow: {
				'xp-window': '2px 2px 4px rgba(0,0,0,0.3)',
				'xp-button': 'inset 1px 1px 0px rgba(255,255,255,0.8), inset -1px -1px 0px rgba(0,0,0,0.3)',
				'xp-pressed': 'inset -1px -1px 0px rgba(255,255,255,0.8), inset 1px 1px 0px rgba(0,0,0,0.3)',
			}
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
