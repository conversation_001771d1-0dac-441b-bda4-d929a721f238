
import React, { useState } from 'react';
import { Slider } from './ui/slider';
import { X } from 'lucide-react';

const MixerSlider = ({ label, value, onValueChange, disabled = false }: { label: string, value: number[], onValueChange: (value: number[]) => void, disabled?: boolean }) => {
    const labelParts = label.split(' ');
    return (
        <div className="flex flex-col items-center space-y-2">
            <div className="h-32">
                <Slider
                    orientation="vertical"
                    value={value}
                    onValueChange={onValueChange}
                    max={100}
                    step={1}
                    disabled={disabled}
                />
            </div>
            <label className="select-none text-center text-[10px] leading-tight w-12 sm:w-14">
                {labelParts[0]}
                <br />
                {labelParts.slice(1).join(' ')}
            </label>
        </div>
    );
}

const VolumeMixer = ({ onClose }: { onClose?: () => void }) => {
    const [enthusiasm, setEnthusiasm] = useState([80]);
    const [collaboration, setCollaboration] = useState([90]);
    const [problemSolving, setProblemSolving] = useState([95]);
    const [learningCurve, setLearningCurve] = useState([75]);
    const [coffee, setCoffee] = useState([100]);

    return (
        <div 
            className="w-[90vw] max-w-96 bg-[#ECE9D8] border border-gray-400 rounded-sm p-1 font-tahoma text-xs shadow-lg text-black"
            style={{
                fontFamily: 'Tahoma, sans-serif',
                boxShadow: '2px 2px 4px rgba(0,0,0,0.5), inset 1px 1px 0px #FFF, inset -1px -1px 0px #848278',
                borderTop: '1px solid #FFFFFF',
                borderLeft: '1px solid #FFFFFF',
                borderRightColor: '#A0A0A0',
                borderBottomColor: '#A0A0A0',
                background: '#DFDDCF'
            }}
            onClick={(e) => e.stopPropagation()}
        >
            <div className="flex items-center justify-between mb-3 px-1 py-0.5 bg-gradient-to-r from-[#0058E0] to-[#3891F8]">
                <span className="font-bold text-white select-none">Skill Mixer</span>
                {onClose && (
                    <button
                        onClick={onClose}
                        className="bg-[#E2614A] hover:bg-[#F0715A] w-4 h-4 flex items-center justify-center rounded-sm"
                        style={{
                            borderTop: '1px solid #FFF',
                            borderLeft: '1px solid #FFF',
                            borderRight: '1px solid #000',
                            borderBottom: '1px solid #000',
                        }}
                    >
                        <X size={10} className="text-white" strokeWidth={2.5} />
                    </button>
                )}
            </div>
            <div className="flex justify-around px-3 pb-3 pt-1">
                <MixerSlider label="Enthusiasm Level" value={enthusiasm} onValueChange={setEnthusiasm} />
                <MixerSlider label="Collaboration Skills" value={collaboration} onValueChange={setCollaboration} />
                <MixerSlider label="Problem Solving" value={problemSolving} onValueChange={setProblemSolving} />
                <MixerSlider label="Learning Curve" value={learningCurve} onValueChange={setLearningCurve} />
                <MixerSlider label="Coffee Dependency" value={coffee} onValueChange={setCoffee} disabled />
            </div>
        </div>
    );
};

export default VolumeMixer;
